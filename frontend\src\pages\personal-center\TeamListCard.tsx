import {
  CarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  LogoutOutlined,
  MinusCircleOutlined,
  RightOutlined,
  SearchOutlined,
  SettingOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import {
  Alert,
  Button,
  Col,
  Flex,
  Input,
  message,
  Modal,
  Pagination,
  Row,
  Spin,
  Tooltip,
  Typography,
} from 'antd';
import { ProCard, ProList } from '@ant-design/pro-components';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { usePagination, paginateArray } from '@/utils/paginationUtils';
import { AuthService } from '@/services';
import { TeamService } from '@/services/team';
import type { TeamDetailResponse } from '@/types/api';
import {
  getTeamIdFromCurrentToken,
  hasTeamInCurrentToken,
  getUserIdFromCurrentToken,
} from '@/utils/tokenUtils';
import { recordTeamSelection, hasUserSelectedTeam } from '@/utils/teamSelectionUtils';
import TeamManagementModal from './components/TeamManagementModal';

const { Text, Title } = Typography;
 



// 响应式布局样式
const styles = `
  .team-item .ant-pro-card-body {
    padding: 0 !important;
  }

  .team-item {
    margin-bottom: 12px !important; /* 减少团队项目间距 */
  }



  @media (max-width: 768px) {
    .team-item {
      margin-bottom: 10px !important; /* 中等屏幕减少团队项目间距 */
    }

    .team-stats-row {
      margin-top: 6px; /* 减少内部间距 */
    }

    .team-info-wrap {
      gap: 6px !important; /* 减少内部间距 */
    }
  }

  @media (max-width: 576px) {
    .team-item {
      margin-bottom: 8px !important; /* 小屏幕减少团队项目间距 */
    }

    .team-stats-row {
      margin-top: 8px; /* 减少内部间距 */
    }

    .team-stats-col {
      margin-bottom: 4px; /* 减少内部间距 */
    }

    .team-info-wrap {
      gap: 6px !important; /* 减少内部间距 */
    }

    .team-meta-info {
      flex-wrap: wrap;
      gap: 6px !important; /* 减少内部间距 */
    }

    .team-status-badges {
      flex-wrap: wrap;
      gap: 4px !important; /* 减少内部间距 */
      margin-top: 4px; /* 减少内部间距 */
    }
  }

  @media (max-width: 480px) {
    .team-item {
      margin-bottom: 6px !important; /* 最小屏幕减少团队项目间距 */
    }

    .team-name-text {
      font-size: 16px !important; /* 保持字体大小 */
    }

    .team-meta-text {
      font-size: 13px !important; /* 保持字体大小 */
    }

    .team-meta-info {
      gap: 4px !important; /* 减少内部间距 */
    }

    .team-status-badges {
      gap: 3px !important; /* 减少内部间距 */
    }
  }
`;

/**
 * 团队列表卡片组件
 *
 * 这是个人中心页面的核心组件，负责显示用户所属的团队列表，
 * 并提供团队切换、创建团队等功能。是团队管理系统的重要入口。
 *
 * 主要功能：
 * 1. 显示用户所属的所有团队
 * 2. 支持团队切换功能
 * 3. 支持创建新团队
 * 4. 显示当前选择的团队状态
 * 5. 处理团队切换过程中的状态管理
 *
 * 状态管理：
 * - 团队列表数据的获取和显示
 * - 团队切换过程的加载状态
 * - 创建团队模态框的状态
 * - 错误状态的处理和显示
 *
 * 团队切换逻辑：
 * 1. 检查用户登录状态
 * 2. 判断是否为当前团队（避免重复切换）
 * 3. 调用后端API进行团队切换
 * 4. 更新本地Token和全局状态
 * 5. 跳转到团队仪表盘
 *
 * 与全局状态的集成：
 * - 监听用户登录状态变化
 * - 同步团队切换后的状态更新
 * - 处理用户注销时的状态清理
 */
const TeamListCard: React.FC = () => {
  /**
   * 团队列表相关状态管理
   *
   * 这些状态用于管理团队列表的显示和交互：
   * - teams: 用户所属的团队列表数据
   * - loading: 团队列表加载状态
   * - error: 错误信息（如网络错误、权限错误等）
   * - switchingTeamId: 当前正在切换的团队ID（用于显示加载状态）
   */
  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);

  /**
   * 搜索功能相关状态管理
   *
   * 这些状态用于管理团队搜索功能：
   * - searchText: 用户输入的搜索文本
   * - debouncedSearchText: 去抖动后的搜索文本，用于实际过滤
   */
  const [searchText, setSearchText] = useState<string>('');
  const [debouncedSearchText, setDebouncedSearchText] = useState<string>('');

  /**
   * 分页功能
   */
  const { pagination, updateTotal } = usePagination({
    defaultPageSize: 6,
    pageSizeOptions: ['6', '12', '18', '24'],
    showTotal: (total, range) => `共 ${total} 个团队，显示第 ${range[0]}-${range[1]} 个`,
  });

  // 模态框状态管理
  const [teamManagementModalVisible, setTeamManagementModalVisible] = useState(false);
  const [leaveTeamModalVisible, setLeaveTeamModalVisible] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState<TeamDetailResponse | null>(null);

  /**
   * 创建团队功能已移至设置页面
   *
   * 为了更好的用户体验和功能组织，创建团队功能已经移动到
   * 专门的设置页面中。用户可以通过"团队设置"按钮跳转到
   * 设置页面进行团队创建和管理操作。
   */

  /**
   * 全局状态管理
   *
   * 从UmiJS全局状态中获取用户和团队信息：
   * - initialState: 包含当前用户和团队信息的全局状态
   * - setInitialState: 更新全局状态的函数
   * - currentTeam: 当前选择的团队信息
   */
  const { initialState, setInitialState } = useModel('@@initialState');
  const currentTeam = initialState?.currentTeam;

  /**
   * Token信息提取
   *
   * 从当前存储的Token中提取关键信息，用于状态判断和权限检查：
   * - currentTokenTeamId: Token中包含的团队ID
   * - currentUserId: Token中包含的用户ID
   * - hasTeamInToken: Token是否包含团队信息
   *
   * 这些信息用于：
   * - 判断当前是否已选择团队
   * - 确定哪个团队是当前激活的团队
   * - 记录用户的团队选择历史
   */
  const currentTokenTeamId = getTeamIdFromCurrentToken();
  const currentUserId = getUserIdFromCurrentToken();
  const hasTeamInToken = hasTeamInCurrentToken();

  // 判断是否有真正的当前团队：
  // 1. Token中有团队信息（说明用户已经选择过团队）
  // 2. initialState中有团队信息（说明已经获取过团队详情）
  // 3. 两者的团队ID一致（确保状态同步）
  // 4. 用户曾经主动选择过这个团队（区分初始登录和主动选择）
  const hasRealCurrentTeam = !!(
    hasTeamInToken &&
    currentTokenTeamId &&
    currentTeam &&
    currentTeam.id === currentTokenTeamId &&
    currentUserId &&
    hasUserSelectedTeam(currentUserId, currentTokenTeamId)
  );

  // 获取实际的当前团队ID：只有在用户真正选择过团队且状态同步时才返回团队ID
  const actualCurrentTeamId = hasRealCurrentTeam ? currentTokenTeamId : null;

  // 调试日志
  console.log('TeamListCard 状态调试:', {
    currentTeam: currentTeam?.id,
    currentTokenTeamId,
    currentUserId,
    hasTeamInToken,
    hasRealCurrentTeam,
    actualCurrentTeamId,
    hasUserSelectedCurrentTeam: currentUserId && currentTokenTeamId ? hasUserSelectedTeam(currentUserId, currentTokenTeamId) : false,
    initialStateCurrentUser: !!initialState?.currentUser,
  });

  // 获取团队列表数据
  const fetchTeams = async () => {
    try {
      setLoading(true);
      setError(null);
      const teamsData = await TeamService.getUserTeamsWithStats();
      setTeams(teamsData);
    } catch (error) {
      console.error('获取团队列表失败:', error);
      setError('获取团队列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 只有在用户已登录时才获取团队列表
    if (initialState?.currentUser) {
      fetchTeams();
    }
  }, [initialState?.currentUser]);

  // 监听全局状态变化，处理注销等情况
  useEffect(() => {
    // 如果用户已注销（currentUser为undefined），清除本地团队列表状态
    if (!initialState?.currentUser) {
      setTeams([]);
      setError(null);
      setLoading(false);
      setSwitchingTeamId(null);
    }
  }, [initialState?.currentUser]);

  // 监听当前团队状态变化
  useEffect(() => {
    console.log('当前团队状态变化:', {
      currentTeam: currentTeam?.id,
      actualCurrentTeamId,
      hasRealCurrentTeam,
    });
  }, [currentTeam?.id, actualCurrentTeamId, hasRealCurrentTeam]);

  /**
   * 去抖动搜索实现
   *
   * 使用useEffect监听searchText变化，在用户停止输入300ms后
   * 更新debouncedSearchText，避免频繁的过滤操作，提升性能。
   */
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(searchText);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchText]);

  /**
   * 团队过滤逻辑
   *
   * 使用useMemo优化过滤性能，只有在teams或debouncedSearchText变化时才重新计算。
   * 实现不区分大小写的团队名称搜索过滤。
   */
  const filteredTeams = useMemo(() => {
    if (!debouncedSearchText.trim()) {
      return teams;
    }

    const searchLower = debouncedSearchText.toLowerCase().trim();
    return teams.filter((team) =>
      team.name.toLowerCase().includes(searchLower)
    );
  }, [teams, debouncedSearchText]);

  /**
   * 分页处理的团队数据
   */
  const paginatedTeams = useMemo(() => {
    const result = paginateArray(filteredTeams, pagination.current, pagination.pageSize);
    // 更新总数
    updateTotal(result.total);
    return result;
  }, [filteredTeams, pagination.current, pagination.pageSize, updateTotal]);

  // 创建团队功能已移至设置页面，此处不再需要处理函数

  /**
   * 搜索功能相关处理函数
   */

  /**
   * 处理搜索输入变化
   */
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
  }, []);

  /**
   * 处理搜索键盘事件
   */
  const handleSearchKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Escape') {
      setSearchText('');
      e.currentTarget.blur(); // 失去焦点
    }
  }, []);

  /**
   * 处理搜索清除
   */
  const handleSearchClear = useCallback(() => {
    setSearchText('');
  }, []);

  /**
   * 团队切换处理函数
   *
   * 这是团队切换功能的核心函数，处理用户从一个团队切换到另一个团队的完整流程。
   * 包括权限检查、API调用、状态更新、页面跳转等步骤。
   *
   * 切换流程：
   * 1. 用户登录状态检查
   * 2. 当前团队状态判断（避免重复切换）
   * 3. 调用后端团队选择API
   * 4. 验证切换结果
   * 5. 更新本地Token和全局状态
   * 6. 记录用户选择历史
   * 7. 跳转到团队仪表盘
   *
   * 状态管理：
   * - 设置切换加载状态（防止重复点击）
   * - 更新全局用户和团队状态
   * - 处理切换过程中的错误状态
   *
   * 错误处理：
   * - 网络错误：显示网络连接提示
   * - 权限错误：由响应拦截器统一处理
   * - 业务错误：显示具体的错误信息
   *
   * @param teamId 要切换到的团队ID
   * @param teamName 团队名称（用于显示消息）
   */
  const handleTeamSwitch = async (teamId: number, teamName: string) => {
    /**
     * 用户登录状态检查
     *
     * 确保用户已登录才能进行团队切换操作。
     * 虽然组件层面已有登录检查，但这里再次确认以确保安全性。
     */
    if (!initialState?.currentUser) {
      return;
    }

    try {
      /**
       * 设置切换状态
       *
       * 标记当前正在切换的团队ID，用于：
       * 1. 在UI上显示加载状态
       * 2. 防止用户重复点击
       * 3. 提供视觉反馈
       */
      setSwitchingTeamId(teamId);

      /**
       * 当前团队检查
       *
       * 如果用户点击的是当前已选择的团队，直接跳转到仪表盘，
       * 避免不必要的API调用和Token更新。
       */
      if (teamId === actualCurrentTeamId) {
        history.push('/dashboard');
        return;
      }

      /**
       * 执行团队切换API调用
       *
       * 调用后端的团队选择接口，后端会：
       * 1. 验证用户是否有权限访问该团队
       * 2. 生成包含新团队信息的JWT Token
       * 3. 返回团队详细信息和切换状态
       */
      const response = await AuthService.selectTeam({ teamId });

      /**
       * 验证切换结果
       *
       * 检查后端返回的响应是否表示切换成功：
       * - teamSelectionSuccess: 切换成功标识
       * - team: 新团队的详细信息
       * - team.id: 确认返回的团队ID与请求的一致
       */
      if (
        response.teamSelectionSuccess &&
        response.team &&
        response.team.id === teamId
      ) {
        /**
         * 记录用户选择历史
         *
         * 将用户的团队选择记录到本地存储，用于：
         * - 下次登录时的默认团队选择
         * - 用户行为分析
         * - 提升用户体验
         */
        if (currentUserId) {
          recordTeamSelection(currentUserId, teamId);
        }

        /**
         * 异步更新全局状态
         *
         * 由于Token已经更新，需要同步更新全局状态中的用户和团队信息。
         * 使用异步更新避免阻塞页面跳转，提升用户体验。
         *
         * 更新流程：
         * 1. 并行获取最新的用户信息和团队信息
         * 2. 验证获取的团队信息是否正确
         * 3. 更新全局状态
         * 4. 处理更新过程中的错误
         */
        if (
          initialState?.fetchTeamInfo &&
          initialState?.fetchUserInfo &&
          setInitialState
        ) {
          // 异步更新状态，不阻塞跳转
          Promise.all([
            initialState.fetchUserInfo(),
            initialState.fetchTeamInfo(),
          ])
            .then(([currentUser, currentTeam]) => {
              // 确认获取的团队信息与切换的团队一致
              if (currentTeam && currentTeam.id === teamId) {
                setInitialState({
                  ...initialState,
                  currentUser,
                  currentTeam,
                });
              }
            })
            .catch((error) => {
              console.error('更新 initialState 失败:', error);
              // 状态更新失败不影响团队切换的核心功能
            });
        }

        /**
         * 页面跳转
         *
         * 切换成功后跳转到团队仪表盘。
         * 路由守卫会验证新的Token并允许访问团队页面。
         */
        history.push('/dashboard');
      } else {
        /**
         * 切换失败处理
         *
         * 如果后端返回的响应不符合预期，说明切换失败。
         * 记录错误日志并提示用户重试。
         */
        // 团队切换响应异常，未返回正确的团队信息
      }
    } catch (error: any) {
      /**
       * 异常处理
       *
       * 处理团队切换过程中可能出现的各种异常：
       * - 网络错误：连接超时、服务器不可达等
       * - 权限错误：用户无权限访问该团队
       * - 业务错误：团队不存在、状态异常等
       *
       * 错误处理策略：
       * 1. 记录详细的错误日志用于调试
       * 2. 响应拦截器已处理大部分错误消息
       * 3. 只对网络错误显示通用提示
       */
      // 错误处理由响应拦截器统一处理
    } finally {
      /**
       * 清理切换状态
       *
       * 无论切换成功还是失败，都要清除切换状态，
       * 恢复UI的正常状态，允许用户进行下一次操作。
       */
      setSwitchingTeamId(null);
    }
  };

  /**
   * 处理团队管理
   */
  const handleTeamManagement = async (team: TeamDetailResponse) => {
    try {
      // 先切换到目标团队以确保有正确的权限
      await AuthService.selectTeam({ teamId: team.id });
      setSelectedTeam(team);
      setTeamManagementModalVisible(true);
    } catch (error) {
      console.error('切换团队失败:', error);
      message.error('无法打开团队管理');
    }
  };

  /**
   * 处理退出团队
   */
  const handleLeaveTeam = (team: TeamDetailResponse) => {
    setSelectedTeam(team);
    setLeaveTeamModalVisible(true);
  };

  /**
   * 确认退出团队
   */
  const confirmLeaveTeam = async () => {
    if (!selectedTeam) return;

    try {
      // 先切换到目标团队
      await AuthService.selectTeam({ teamId: selectedTeam.id });

      // 退出团队
      await TeamService.leaveTeam();

      // 重新获取团队列表
      await fetchTeams();

      // 更新全局状态，清除当前团队
      if (setInitialState) {
        await setInitialState((prevState) => ({
          ...prevState,
          currentTeam: undefined,
        }));
      }

      message.success('已成功退出团队');
      setLeaveTeamModalVisible(false);
      setSelectedTeam(null);
    } catch (error) {
      console.error('退出团队失败:', error);
      message.error('退出团队失败');
    }
  };



  return (
    <>
      {/* 注入样式 */}
      {/* <style dangerouslySetInnerHTML={{ __html: styles }} /> */}

      <ProCard
        title="团队列表"
        style={{
          marginBottom: 24,
          borderRadius: 8,
          border: '1px solid #d9d9d9',
        }}
      >
        {/* 搜索输入框 */}
        {initialState?.currentUser && teams.length > 0 && (
          <div style={{ marginBottom: 16 }}>
            <Row gutter={[16, 0]}>
              <Col xs={24} sm={24} md={16} lg={18} xl={20}>
                <Input.Search
                  placeholder="搜索团队名称..."
                  allowClear
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={handleSearchChange}
                  onKeyDown={handleSearchKeyDown}
                  style={{ width: '100%' }}
                  size="middle"
                />
              </Col>
            </Row>
          </div>
        )}

        {/* 团队搜索结果统计 */}
        {initialState?.currentUser && teams.length > 0 && !loading && !error && (
          <div style={{
            marginBottom: 16,
            padding: '8px 12px',
            background: '#f8f9fa',
            borderRadius: 6,
            border: '1px solid #e8e8e8'
          }}>
            <Text type="secondary" style={{ fontSize: 14 }}>
              {debouncedSearchText.trim() ? (
                filteredTeams.length > 0 ? (
                  <>找到 <Text strong style={{ color: '#1890ff' }}>{filteredTeams.length}</Text> 个团队</>
                ) : (
                  <>找到 <Text strong style={{ color: '#ff4d4f' }}>0</Text> 个团队</>
                )
              ) : (
                <>总计：<Text strong style={{ color: '#52c41a' }}>{teams.length}</Text> 个团队</>
              )}
            </Text>
          </div>
        )}

        {error ? (
          <Alert
            message="团队列表加载失败"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
        ) : (
          <Spin spinning={loading}>
            {!initialState?.currentUser ? (
              <div style={{ textAlign: 'center', padding: '40px 20px' }}>
                <Text type="secondary">请先登录以查看团队列表</Text>
              </div>
            ) : teams.length === 0 && !loading ? (
              <div style={{ textAlign: 'center', padding: '40px 20px' }}>
                <Text type="secondary">暂无团队，请先加入或创建团队</Text>
              </div>
            ) : filteredTeams.length === 0 && debouncedSearchText.trim() ? (
              <div style={{ textAlign: 'center', padding: '40px 20px' }}>
                <Text type="secondary">未找到匹配的团队</Text>
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary" style={{ fontSize: 14 }}>
                    尝试使用不同的关键词搜索
                  </Text>
                </div>
              </div>
            ) : (
              <>
                <ProList
                  dataSource={paginatedTeams.list}
                split={false} /* 移除默认分割线 */
                itemLayout="vertical" /* 垂直布局 */
                renderItem={(item) => (
                  <ProCard
                    className="team-item"
                    style={{
                      marginBottom: 10,
                      backgroundColor:
                        actualCurrentTeamId === item.id
                          ? '#f0f9ff'
                          : '#fff',
                      borderRadius: 8,
                      width: '100%',
                      borderLeft: `3px solid ${item.isCreator ? '#722ed1' : '#52c41a'}`,
                      border:
                        actualCurrentTeamId === item.id
                          ? '1px solid #91caff'
                          : '1px solid #d9d9d9',
                   
                      position: 'relative',
                    }}
                    >
                      {/* 响应式布局 */}
                      <Row
                        gutter={[8, 8]}
                        align="middle"
                        style={{ width: '100%' }}
                      >
                        {/* 左侧：团队信息 */}
                        <Col xs={24} sm={24} md={14} lg={12} xl={14}>
                          <Flex vertical gap={8} className="team-info-wrap"> {/* 减少垂直间距 */}
                            {/* 团队名称行 */}
                            <Flex align="center" gap={8} wrap="wrap"> {/* 减少水平间距 */}
                              <div
                                style={{
                                  cursor: 'pointer',
                                  padding: '2px 4px',
                                  borderRadius: 4,
                                  transition: 'all 0.2s ease',
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 6,
                                }}
                                onClick={() =>
                                  handleTeamSwitch(item.id, item.name)
                                }
                                onMouseEnter={(e) => {
                                  e.currentTarget.style.background =
                                    'rgba(24, 144, 255, 0.05)';
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.style.background =
                                    'transparent';
                                }}
                              >
                                <Text
                                  strong
                                  style={{
                                    fontSize: 20, /* 增大团队名称字体 */
                                    color:
                                      actualCurrentTeamId === item.id
                                        ? '#1890ff'
                                        : '#262626',
                                    lineHeight: 1.3, /* 增加行高 */
                                  }}
                                >
                                  {item.name}
                                </Text>
                                <RightOutlined
                                  style={{
                                    fontSize: 12, /* 增大图标大小 */
                                    color:
                                      actualCurrentTeamId === item.id
                                        ? '#1890ff'
                                        : '#8c8c8c',
                                    verticalAlign: 'middle',
                                    display: 'inline-flex',
                                    alignItems: 'center',
                                  }}
                                />
                              </div>

                              {/* 状态标识 */}
                              {actualCurrentTeamId === item.id && (
                                <span
                                  style={{
                                    background: '#1890ff',
                                    color: 'white',
                                    padding: '3px 8px', /* 增加内边距 */
                                    borderRadius: 8,
                                    fontSize: 12, /* 增大字体 */
                                    fontWeight: 500,
                                  }}
                                >
                                  当前
                                </span>
                              )}



                              {switchingTeamId === item.id && (
                                <Flex align="center" gap={4}>
                                  <Spin size="small" />
                                  <Text style={{ fontSize: 12, color: '#666' }}> {/* 增大字体 */}
                                    切换中
                                  </Text>
                                </Flex>
                              )}
                            </Flex>

                            {/* 团队基本信息 */}
                            <Flex align="center" gap={12} wrap="wrap" className="team-meta-info"> {/* 减少间距 */}
                              <Tooltip
                                title={`团队创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`}
                              >
                                <Flex align="center" gap={4}> {/* 减少间距 */}
                                  <ClockCircleOutlined
                                    style={{ color: '#8c8c8c', fontSize: 14 }} /* 增大图标 */
                                  />
                                  <Text
                                    style={{ fontSize: 14, color: '#8c8c8c' }} /* 增大字体 */
                                  >
                                    创建: {new Date(
                                      item.createdAt,
                                    ).toLocaleDateString('zh-CN')}
                                  </Text>
                                </Flex>
                              </Tooltip>

                              {/* 加入日期 */}
                              {item.assignedAt && (
                                <Tooltip
                                  title={`加入团队时间: ${new Date(item.assignedAt).toLocaleString('zh-CN')}`}
                                >
                                  <Flex align="center" gap={4}> {/* 减少间距 */}
                                    <UserOutlined
                                      style={{ color: '#8c8c8c', fontSize: 14 }} /* 增大图标 */
                                    />
                                    <Text
                                      style={{ fontSize: 14, color: '#8c8c8c' }} /* 增大字体 */
                                    >
                                      加入: {new Date(
                                        item.assignedAt,
                                      ).toLocaleDateString('zh-CN')}
                                    </Text>
                                  </Flex>
                                </Tooltip>
                              )}

                              <Tooltip
                                title={`团队成员: ${item.memberCount}人`}
                              >
                                <Flex align="center" gap={4}> {/* 减少间距 */}
                                  <TeamOutlined
                                    style={{ color: '#8c8c8c', fontSize: 14 }} /* 增大图标 */
                                  />
                                  <Text
                                    style={{ fontSize: 14, color: '#8c8c8c' }} /* 增大字体 */
                                  >
                                    {item.memberCount} 人
                                  </Text>
                                </Flex>
                              </Tooltip>
                            </Flex>

                            {/* 状态标识行 */}
                            <Flex align="center" gap={8} wrap="wrap" className="team-status-badges"> {/* 减少间距 */}
                              {/* 身份标识 */}
                              <span
                                style={{
                                  background: item.isCreator ? '#722ed1' : '#1890ff',
                                  color: 'white',
                                  padding: '4px 8px',
                                  borderRadius: 8,
                                  fontSize: 12,
                                  fontWeight: 500,
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 4,
                                }}
                              >
                                {item.isCreator ? (
                                  <>
                                    <UserOutlined style={{ fontSize: 11 }} />
                                    团队创建者
                                  </>
                                ) : (
                                  <>
                                    <TeamOutlined style={{ fontSize: 11 }} />
                                    团队成员
                                  </>
                                )}
                              </span>

                              {/* 用户状态标识 */}
                              <span
                                style={{
                                  background: item.isActive ? '#52c41a' : '#ff4d4f',
                                  color: 'white',
                                  padding: '4px 8px', /* 增加内边距 */
                                  borderRadius: 8,
                                  fontSize: 12, /* 增大字体 */
                                  fontWeight: 500,
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 2,
                                }}
                              >
                                {item.isActive ? (
                                  <>
                                    <CheckCircleOutlined style={{ fontSize: 11 }} /> {/* 增大图标 */}
                                    启用
                                  </>
                                ) : (
                                  <>
                                    <MinusCircleOutlined style={{ fontSize: 11 }} /> {/* 增大图标 */}
                                    停用
                                  </>
                                )}
                              </span>

                              {/* 操作按钮 - 移动到状态信息之后 */}
                              {item.isCreator ? (
                                <Tooltip title="团队管理">
                                  <Button
                                    type="text"
                                    size="small"
                                    icon={<SettingOutlined style={{ fontSize: 12 }} />}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleTeamManagement(item);
                                    }}
                                    style={{
                                      color: '#722ed1',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      width: 24,
                                      height: 24,
                                      padding: 0,
                                      borderRadius: 4,
                                      border: '1px solid #d3adf7',
                                      background: 'rgba(114, 46, 209, 0.04)',
                                      transition: 'all 0.2s ease',
                                    }}
                                    onMouseEnter={(e) => {
                                      e.currentTarget.style.background = 'rgba(114, 46, 209, 0.1)';
                                      e.currentTarget.style.borderColor = '#722ed1';
                                    }}
                                    onMouseLeave={(e) => {
                                      e.currentTarget.style.background = 'rgba(114, 46, 209, 0.04)';
                                      e.currentTarget.style.borderColor = '#d3adf7';
                                    }}
                                  />
                                </Tooltip>
                              ) : (
                                <Tooltip title="退出团队">
                                  <Button
                                    type="text"
                                    size="small"
                                    icon={<LogoutOutlined style={{ fontSize: 12 }} />}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleLeaveTeam(item);
                                    }}
                                    style={{
                                      color: '#ff4d4f',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      width: 24,
                                      height: 24,
                                      padding: 0,
                                      borderRadius: 4,
                                      border: '1px solid #ffccc7',
                                      background: 'rgba(255, 77, 79, 0.04)',
                                      transition: 'all 0.2s ease',
                                    }}
                                    onMouseEnter={(e) => {
                                      e.currentTarget.style.background = 'rgba(255, 77, 79, 0.1)';
                                      e.currentTarget.style.borderColor = '#ff4d4f';
                                    }}
                                    onMouseLeave={(e) => {
                                      e.currentTarget.style.background = 'rgba(255, 77, 79, 0.04)';
                                      e.currentTarget.style.borderColor = '#ffccc7';
                                    }}
                                  />
                                </Tooltip>
                              )}
                            </Flex>
                          </Flex>
                        </Col>

                        {/* 右侧：响应式指标卡片和操作按钮 */}
                        <Col xs={24} sm={24} md={10} lg={12} xl={10}>
                          <Row
                            gutter={[6, 6]} /* 减少间距 */
                            justify={{ xs: 'start', md: 'end' }}
                            align="middle"
                          >
                            {/* 车辆资源 */}
                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>
                              <div
                                style={{
                                  background: '#f0f7ff',
                                  border: '1px solid #d9e8ff',
                                  borderRadius: 6,
                                  padding: '6px 8px', /* 减少内边距 */
                                  textAlign: 'center',
                                  minWidth: '55px', /* 增加最小宽度 */
                                }}
                              >
                                <Flex vertical align="center" gap={2}> {/* 减少间距 */}
                                  <CarOutlined
                                    style={{ color: '#1890ff', fontSize: 16 }} /* 增大图标 */
                                  />
                                  <Text
                                    strong
                                    style={{
                                      fontSize: 18, /* 增大数字字体 */
                                      color: '#1890ff',
                                      lineHeight: 1.2, /* 增加行高 */
                                    }}
                                  >
                                    {item.stats?.vehicles || 0}
                                  </Text>
                                  <Text style={{ fontSize: 11, color: '#666' }}> {/* 增大标签字体 */}
                                    车辆
                                  </Text>
                                </Flex>
                              </div>
                            </Col>

                            {/* 人员资源 */}
                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>
                              <div
                                style={{
                                  background: '#f6ffed',
                                  border: '1px solid #d1f0be',
                                  borderRadius: 6,
                                  padding: '6px 8px', /* 减少内边距 */
                                  textAlign: 'center',
                                  minWidth: '55px', /* 保持最小宽度 */
                                }}
                              >
                                <Flex vertical align="center" gap={2}> {/* 减少间距 */}
                                  <UserOutlined
                                    style={{ color: '#52c41a', fontSize: 16 }} /* 增大图标 */
                                  />
                                  <Text
                                    strong
                                    style={{
                                      fontSize: 18, /* 增大数字字体 */
                                      color: '#52c41a',
                                      lineHeight: 1.2, /* 增加行高 */
                                    }}
                                  >
                                    {item.stats?.personnel || 0}
                                  </Text>
                                  <Text style={{ fontSize: 11, color: '#666' }}> {/* 增大标签字体 */}
                                    人员
                                  </Text>
                                </Flex>
                              </div>
                            </Col>

                            {/* 临期事项 */}
                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>
                              <div
                                style={{
                                  background: '#fff7e6',
                                  border: '1px solid #ffd666',
                                  borderRadius: 6,
                                  padding: '6px 8px', /* 减少内边距 */
                                  textAlign: 'center',
                                  minWidth: '55px', /* 保持最小宽度 */
                                }}
                              >
                                <Flex vertical align="center" gap={2}> {/* 减少间距 */}
                                  <ExclamationCircleOutlined
                                    style={{ color: '#faad14', fontSize: 16 }} /* 增大图标 */
                                  />
                                  <Text
                                    strong
                                    style={{
                                      fontSize: 18, /* 增大数字字体 */
                                      color: '#faad14',
                                      lineHeight: 1.2, /* 增加行高 */
                                    }}
                                  >
                                    {item.stats?.expiring || 0}
                                  </Text>
                                  <Text style={{ fontSize: 11, color: '#666' }}> {/* 增大标签字体 */}
                                    临期
                                  </Text>
                                </Flex>
                              </div>
                            </Col>

                            {/* 逾期事项 */}
                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>
                              <div
                                style={{
                                  background: '#fff1f0',
                                  border: '1px solid #ffccc7',
                                  borderRadius: 6,
                                  padding: '6px 8px', /* 减少内边距 */
                                  textAlign: 'center',
                                  minWidth: '55px', /* 保持最小宽度 */
                                }}
                              >
                                <Flex vertical align="center" gap={2}> {/* 减少间距 */}
                                  <ExclamationCircleOutlined
                                    style={{ color: '#ff4d4f', fontSize: 16 }} /* 增大图标 */
                                  />
                                  <Text
                                    strong
                                    style={{
                                      fontSize: 18, /* 增大数字字体 */
                                      color: '#ff4d4f',
                                      lineHeight: 1.2, /* 增加行高 */
                                    }}
                                  >
                                    {item.stats?.overdue || 0}
                                  </Text>
                                  <Text style={{ fontSize: 11, color: '#666' }}> {/* 增大标签字体 */}
                                    逾期
                                  </Text>
                                </Flex>
                              </div>
                            </Col>


                          </Row>
                        </Col>
                      </Row>
                    </ProCard>
                )}
              />
            )}
          </Spin>
        )}
      </ProCard>

      {/* 团队管理模态框 */}
      <TeamManagementModal
        visible={teamManagementModalVisible}
        onCancel={() => {
          setTeamManagementModalVisible(false);
          setSelectedTeam(null);
        }}
        team={selectedTeam}
        onRefresh={fetchTeams}
      />

      {/* 退出团队确认模态框 */}
      <Modal
        title="退出团队"
        open={leaveTeamModalVisible}
        onCancel={() => {
          setLeaveTeamModalVisible(false);
          setSelectedTeam(null);
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setLeaveTeamModalVisible(false);
              setSelectedTeam(null);
            }}
          >
            取消
          </Button>,
          <Button
            key="leave"
            type="primary"
            danger
            onClick={confirmLeaveTeam}
          >
            确认退出
          </Button>,
        ]}
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <p>确定要退出团队 <strong>{selectedTeam?.name}</strong> 吗？</p>
          <p style={{ color: '#ff4d4f' }}>退出后您将无法访问该团队的资源和数据</p>
        </div>
      </Modal>

      {/* 创建团队功能已移至设置页面 */}
    </>
  );
};

export default TeamListCard;
