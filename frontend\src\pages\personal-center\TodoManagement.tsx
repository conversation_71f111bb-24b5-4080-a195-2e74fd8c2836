import {
  CalendarOutlined,
  CheckOutlined,
  DeleteOutlined,
  EditOutlined,
  MoreOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Button,
  Col,
  Dropdown,
  Flex,
  Form,
  Input,
  Progress,
  Row,
  Select,
  Space,
  Spin,
  Tabs,
  Tooltip,
  Typography,
} from 'antd';
import { ModalForm, ProList, ProCard } from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';
import { TodoService } from '@/services/todo';
import type { TodoResponse, TodoStatsResponse } from '@/types/api';

const { Text } = Typography;
const { TabPane } = Tabs;

// 使用API类型定义，不需要重复定义接口
interface TodoManagementProps {
  onAddTodo?: (todo: TodoResponse) => void;
  onUpdateTodo?: (id: number, updatedTodo: Partial<TodoResponse>) => void;
  onDeleteTodo?: (id: number) => void;
}

const TodoManagement: React.FC<TodoManagementProps> = () => {
  // TODO数据状态管理
  const [personalTasks, setPersonalTasks] = useState<TodoResponse[]>([]);
  const [todoStats, setTodoStats] = useState<TodoStatsResponse>({
    highPriorityCount: 0,
    mediumPriorityCount: 0,
    lowPriorityCount: 0,
    totalCount: 0,
    completedCount: 0,
    completionPercentage: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 待办事项状态管理
  const [todoModalVisible, setTodoModalVisible] = useState(false);
  const [todoForm] = Form.useForm();
  const [editingTodoId, setEditingTodoId] = useState<number | null>(null);

  // 过滤器状态
  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'completed'>(
    'pending',
  );
  const [searchText, setSearchText] = useState('');

  // 获取TODO数据
  useEffect(() => {
    const fetchTodoData = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('TodoManagement: 开始获取TODO数据');

        // 分别获取TODO列表和统计数据，避免一个失败影响另一个
        const todosPromise = TodoService.getUserTodos().catch((error) => {
          console.error('获取TODO列表失败:', error);
          return [];
        });

        const statsPromise = TodoService.getTodoStats().catch((error) => {
          console.error('获取TODO统计失败:', error);
          return {
            highPriorityCount: 0,
            mediumPriorityCount: 0,
            lowPriorityCount: 0,
            totalCount: 0,
            completedCount: 0,
            completionPercentage: 0,
          };
        });

        const [todos, stats] = await Promise.all([todosPromise, statsPromise]);

        console.log('TodoManagement: 获取到TODO列表:', todos);
        console.log('TodoManagement: 获取到统计数据:', stats);

        setPersonalTasks(todos);
        setTodoStats(stats);
      } catch (error) {
        console.error('获取TODO数据时发生未知错误:', error);
        setError('获取TODO数据失败，请刷新页面重试');
      } finally {
        setLoading(false);
      }
    };

    fetchTodoData();
  }, []);

  // 根据激活的标签和搜索文本过滤任务
  const filteredPersonalTasks = (personalTasks || []).filter((task) => {
    // 根据标签过滤
    if (activeTab === 'pending' && task.status === 1) return false;
    if (activeTab === 'completed' && task.status === 0) return false;

    // 根据搜索文本过滤
    if (
      searchText &&
      !task.title.toLowerCase().includes(searchText.toLowerCase())
    ) {
      return false;
    }

    return true;
  });

  // 处理待办事项操作
  const handleToggleTodoStatus = async (id: number) => {
    try {
      const task = personalTasks.find((t) => t.id === id);
      if (!task) {
        return;
      }

      const newStatus = task.status === 0 ? 1 : 0;

      await TodoService.updateTodo(id, { status: newStatus });

      // 更新本地状态
      setPersonalTasks(
        personalTasks.map((task) =>
          task.id === id ? { ...task, status: newStatus } : task,
        ),
      );

      // 刷新统计数据
      try {
        const stats = await TodoService.getTodoStats();
        setTodoStats(stats);
      } catch (statsError) {
        // 统计数据刷新失败不影响主要操作
      }
    } catch (error) {
      // 错误处理由响应拦截器统一处理
    }
  };

  const handleAddOrUpdateTodo = async (values: any) => {
    try {
      if (editingTodoId) {
        // 更新现有待办事项
        const updatedTodo = await TodoService.updateTodo(editingTodoId, {
          title: values.name,
          priority: values.priority,
        });

        setPersonalTasks(
          personalTasks.map((task) =>
            task.id === editingTodoId ? updatedTodo : task,
          ),
        );
      } else {
        // 添加新待办事项
        const newTodo = await TodoService.createTodo({
          title: values.name,
          priority: values.priority,
        });

        setPersonalTasks([newTodo, ...personalTasks]);
      }

      // 刷新统计数据
      try {
        const stats = await TodoService.getTodoStats();
        setTodoStats(stats);
      } catch (statsError) {
        // 统计数据刷新失败不影响主要操作
      }

      // 重置表单并关闭模态框
      setTodoModalVisible(false);
      setEditingTodoId(null);
      todoForm.resetFields();
    } catch (error) {
      // 错误处理由响应拦截器统一处理
    }
  };

  const handleDeleteTodo = async (id: number) => {
    try {
      await TodoService.deleteTodo(id);
      setPersonalTasks(personalTasks.filter((task) => task.id !== id));

      // 刷新统计数据
      try {
        const stats = await TodoService.getTodoStats();
        setTodoStats(stats);
      } catch (statsError) {
        // 统计数据刷新失败不影响主要操作
      }
    } catch (error) {
      // 错误处理由响应拦截器统一处理
    }
  };

  return (
    <ProCard
      title="待办事项/任务列表"
      style={{
        borderRadius: 8,
        height: 'fit-content',
        minHeight: '600px', // 确保右列有足够的高度
      }}
      headStyle={{
        borderBottom: '1px solid #f0f0f0',
        paddingBottom: 12,
      }}
      bodyStyle={{
        padding: '16px',
      }}
    >
      {/* 任务管理界面头部区域 */}
      <div
        style={{
          marginBottom: 16,
          padding: '12px 16px',
          background: '#fafbfc',
          borderRadius: 8,
          border: '1px solid #f0f0f0',
        }}
      >
        {/* 优化的统计信息区域 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          {/* 左侧：优先级统计 */}
          <Col xs={24} sm={14} md={14} lg={16} xl={18}>
            <Flex align="center" gap={12} wrap="wrap">
              {/* 高优先级 */}
              <div
                style={{
                  background: '#fff2f0',
                  border: '1px solid #ffccc7',
                  borderRadius: 6,
                  padding: '8px 12px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 8,
                  minWidth: 100,
                }}
              >
                <div
                  style={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    background: '#ff4d4f',
                  }}
                />
                <Text style={{ fontSize: 12, color: '#8c8c8c', marginRight: 4 }}>
                  高优先级
                </Text>
                <Text style={{ fontSize: 14, fontWeight: 600, color: '#cf1322' }}>
                  {todoStats.highPriorityCount}
                </Text>
              </div>

              {/* 中优先级 */}
              <div
                style={{
                  background: '#fffbe6',
                  border: '1px solid #ffe58f',
                  borderRadius: 6,
                  padding: '8px 12px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 8,
                  minWidth: 100,
                }}
              >
                <div
                  style={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    background: '#faad14',
                  }}
                />
                <Text style={{ fontSize: 12, color: '#8c8c8c', marginRight: 4 }}>
                  中优先级
                </Text>
                <Text style={{ fontSize: 14, fontWeight: 600, color: '#d48806' }}>
                  {todoStats.mediumPriorityCount}
                </Text>
              </div>

              {/* 低优先级 */}
              <div
                style={{
                  background: '#fafafa',
                  border: '1px solid #d9d9d9',
                  borderRadius: 6,
                  padding: '8px 12px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 8,
                  minWidth: 100,
                }}
              >
                <div
                  style={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    background: '#8c8c8c',
                  }}
                />
                <Text style={{ fontSize: 12, color: '#8c8c8c', marginRight: 4 }}>
                  低优先级
                </Text>
                <Text style={{ fontSize: 14, fontWeight: 600, color: '#595959' }}>
                  {todoStats.lowPriorityCount}
                </Text>
              </div>
            </Flex>
          </Col>

          {/* 右侧：完成率 */}
          <Col xs={24} sm={10} md={10} lg={8} xl={6}>
            <div
              style={{
                background: '#f6ffed',
                border: '1px solid #b7eb8f',
                borderRadius: 8,
            
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Tooltip
                title={`完成率: ${todoStats.completionPercentage}% (${todoStats.completedCount}/${todoStats.totalCount})`}
              >
                <Flex align="center" gap={12}>
                  <Text style={{ fontSize: 12, color: '#8c8c8c' }}>
                    完成率
                  </Text>
                  <Progress
                    percent={todoStats.completionPercentage}
                    size="small"
                    style={{ width: 80 }}
                    strokeColor="#52c41a"
                    showInfo={false}
                  />
                  <Text
                    style={{
                      fontSize: 16,
                      fontWeight: 600,
                      color: '#389e0d',
                    }}
                  >
                    {todoStats.completionPercentage}%
                  </Text>
                </Flex>
              </Tooltip>
            </div>
          </Col>
        </Row>

        {/* 第二行：左侧搜索功能，右侧添加新任务按钮 */}
        <Row gutter={[16, 0]}>
          <Col xs={24} sm={24} md={16} lg={18} xl={20}>
            <Input.Search
              placeholder="搜索任务..."
              allowClear
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: '100%' }}
              size="middle"
            />
          </Col>

          <Col xs={24} sm={24} md={8} lg={6} xl={4}>
            <Button
    
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingTodoId(null);
                todoForm.resetFields();
                setTodoModalVisible(true);
              }}
            
              size="middle"
            >
              
            </Button>
          </Col>
        </Row>
      </div>

      {/* 第二行：标签页 */}
      <Tabs
        activeKey={activeTab}
        onChange={(key) => setActiveTab(key as 'all' | 'pending' | 'completed')}
        size="middle"
        style={{ marginBottom: 8 }}
      >
        <TabPane tab="全部" key="all" />
        <TabPane tab="待处理" key="pending" />
        <TabPane tab="已完成" key="completed" />
      </Tabs>

      {/* 待办事项列表 */}
      {error ? (
        <Alert
          message="TODO数据加载失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      ) : (
        <Spin spinning={loading}>
          <ProList
            dataSource={filteredPersonalTasks}
            renderItem={(item) => {
              return (
                <div
                  className="todo-item"
                  style={{
                    padding: '10px 16px',
                    marginBottom: 12,
                    borderRadius: 8,
                    background: '#fff',
                    opacity: item.status === 1 ? 0.7 : 1,
                    borderLeft: `3px solid ${
                      item.status === 1
                        ? '#52c41a'
                        : item.priority === 3
                          ? '#ff4d4f'
                          : item.priority === 2
                            ? '#faad14'
                            : '#8c8c8c'
                    }`,
                    boxShadow: '0 1px 4px rgba(0,0,0,0.05)',
                  }}
                >
                  <Flex align="center" gap={12} style={{ width: '100%' }}>
                    {/* 左侧状态和优先级指示器 */}
                    <Flex vertical align="center">
                      {item.status === 1 ? (
                        <Flex
                          align="center"
                          justify="center"
                          style={{
                            width: 22,
                            height: 22,
                            borderRadius: '50%',
                            background: '#52c41a',
                          }}
                        >
                          <CheckOutlined
                            style={{ color: '#fff', fontSize: 12 }}
                          />
                        </Flex>
                      ) : (
                        <div
                          style={{
                            width: 18,
                            height: 18,
                            borderRadius: '50%',
                            border: `2px solid ${
                              item.priority === 3
                                ? '#ff4d4f'
                                : item.priority === 2
                                  ? '#faad14'
                                  : '#8c8c8c'
                            }`,
                          }}
                        />
                      )}

                      <div
                        style={{
                          width: 2,
                          height: 24,
                          background: '#f0f0f0',
                          marginTop: 4,
                        }}
                      />
                    </Flex>

                    {/* 任务信息区 */}
                    <Flex vertical style={{ flex: 1 }}>
                      <Text
                        style={{
                          fontSize: 14,
                          fontWeight: item.priority === 3 ? 500 : 'normal',
                          textDecoration:
                            item.status === 1 ? 'line-through' : 'none',
                          color: item.status === 1 ? '#8c8c8c' : '#262626',
                        }}
                      >
                        {item.title}
                      </Text>

                      {/* 显示创建日期 */}
                      <Space align="center" size={6} style={{ marginTop: 4 }}>
                        <CalendarOutlined
                          style={{
                            fontSize: 12,
                            color: '#8c8c8c',
                          }}
                        />
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          创建于:{' '}
                          {new Date(item.createdAt).toLocaleDateString('zh-CN')}
                        </Text>
                      </Space>
                    </Flex>

                    {/* 操作按钮区 */}
                    <Dropdown
                      trigger={['click']}
                      menu={{
                        items: [
                          {
                            key: 'complete',
                            label:
                              item.status === 1 ? '标记未完成' : '标记完成',
                            icon: (
                              <CheckOutlined
                                style={{
                                  color:
                                    item.status === 1 ? '#8c8c8c' : '#52c41a',
                                  fontSize: 14,
                                }}
                              />
                            ),
                          },
                          {
                            key: 'edit',
                            label: '编辑任务',
                            icon: <EditOutlined style={{ color: '#8c8c8c' }} />,
                          },
                          {
                            key: 'delete',
                            label: '删除任务',
                            icon: (
                              <DeleteOutlined style={{ color: '#ff4d4f' }} />
                            ),
                            danger: true,
                          },
                        ],
                        onClick: ({ key }) => {
                          if (key === 'complete') {
                            handleToggleTodoStatus(item.id);
                          } else if (key === 'edit') {
                            setEditingTodoId(item.id);
                            todoForm.setFieldsValue({
                              name: item.title,
                              priority: item.priority,
                            });
                            setTodoModalVisible(true);
                          } else if (key === 'delete') {
                            handleDeleteTodo(item.id);
                          }
                        },
                      }}
                    >
                      <Button
                        type="text"
                        size="small"
                        icon={<MoreOutlined />}
                        style={{ width: 32, height: 32 }}
                      />
                    </Dropdown>
                  </Flex>
                </div>
              );
            }}
          />

          {/* 待办事项表单模态框 */}
          <ModalForm
            title={editingTodoId ? '编辑待办事项' : '新增待办事项'}
            open={todoModalVisible}
            onOpenChange={(visible) => {
              setTodoModalVisible(visible);
              if (!visible) {
                setEditingTodoId(null);
                todoForm.resetFields();
              }
            }}
            form={todoForm}
            layout="vertical"
            onFinish={handleAddOrUpdateTodo}
            autoComplete="off"
            width={500}
            modalProps={{
              centered: true,
              destroyOnClose: true,
              maskClosable: true,
              keyboard: true,
              forceRender: false,
            }}
            submitter={{
              searchConfig: {
                submitText: editingTodoId ? '更新任务' : '创建任务',
                resetText: '取消',
              },
              submitButtonProps: {
                style: {
                  background: '#1890ff',
                  borderColor: '#1890ff',
                  boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',
                },
                icon: editingTodoId ? <EditOutlined /> : <PlusOutlined />,
              },
              resetButtonProps: {
                style: {
                  borderColor: '#d9d9d9',
                },
              },
              onReset: () => {
                setTodoModalVisible(false);
                setEditingTodoId(null);
                todoForm.resetFields();
              },
            }}
            preserve={false}
          >
            <Form.Item
              name="name"
              label="任务名称"
              rules={[{ required: true, message: '请输入任务名称' }]}
            >
              <Input
                placeholder="请输入任务名称"
                size="large"
                style={{ borderRadius: 6 }}
              />
            </Form.Item>

            <Form.Item
              name="priority"
              label="优先级"
              initialValue={2}
              rules={[{ required: true, message: '请选择优先级' }]}
            >
              <Select
                size="large"
                options={[
                  { value: 3, label: '高优先级' },
                  { value: 2, label: '中优先级' },
                  { value: 1, label: '低优先级' },
                ]}
                style={{ borderRadius: 6 }}
              />
            </Form.Item>
          </ModalForm>
        </Spin>
      )}
    </ProCard>
  );
};

export default TodoManagement;
