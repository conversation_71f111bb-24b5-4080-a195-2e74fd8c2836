{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.5945132241590510050.hot-update.js", "src/pages/personal-center/DataOverview.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='9605996420717528412';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  BarChartOutlined,\n  CarOutlined,\n  UsergroupAddOutlined,\n  ExclamationCircleOutlined,\n  AlertOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Spin,\n  Grid,\n  Flex,\n  Typography,\n} from 'antd';\nimport { ProCard, StatisticCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserPersonalStatsResponse } from '@/types/api';\n\n/**\n * 数据概览卡片组件\n *\n * 使用 Ant Design Pro Components 的 StatisticCard 组件显示用户的个人统计数据，\n * 采用单行四列的响应式网格布局。包括车辆、人员、预警、告警等指标的统计卡片。\n *\n * 主要功能：\n * 1. 显示车辆数量统计 - 使用车辆图标，蓝色主题\n * 2. 显示人员数量统计 - 使用用户组图标，绿色主题\n * 3. 显示预警数量统计 - 使用感叹号图标，橙色主题\n * 4. 显示告警数量统计 - 使用警告图标，红色主题\n *\n * 数据来源：\n * - 个人统计数据：通过UserService.getUserPersonalStats()获取\n *\n * 布局特点：\n * - 使用 StatisticCard 组件提供专业的数据展示\n * - 单行四列水平排列，响应式布局适配不同屏幕\n * - 每个统计项都有语义化的图标和颜色主题\n * - 统一的卡片样式和高度\n */\nconst DataOverview: React.FC = () => {\n  /**\n   * 响应式检测\n   */\n  const { useBreakpoint } = Grid;\n  const screens = useBreakpoint();\n\n  /**\n   * 个人统计数据状态管理\n   */\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({\n    vehicles: 0,\n    personnel: 0,\n    warnings: 0,\n    alerts: 0,\n  });\n\n  const [statsLoading, setStatsLoading] = useState(true);\n  const [statsError, setStatsError] = useState<string | null>(null);\n\n  // 获取统计数据\n  useEffect(() => {\n    const fetchStatsData = async () => {\n      try {\n        const stats = await UserService.getUserPersonalStats();\n        setPersonalStats(stats);\n        setStatsError(null);\n      } catch (error) {\n        console.error('获取统计数据失败:', error);\n        setStatsError('获取统计数据失败，请稍后重试');\n      } finally {\n        setStatsLoading(false);\n      }\n    };\n\n    fetchStatsData();\n  }, []);\n\n  return (\n    <ProCard\n      title={\n        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\n          <BarChartOutlined style={{ fontSize: 16, color: '#1890ff' }} />\n          <span>数据概览</span>\n        </div>\n      }\n      style={{\n        marginBottom: 16,\n        borderRadius: 8,\n        border: '1px solid #d9d9d9',\n      }}\n      headStyle={{\n        borderBottom: '1px solid #f0f0f0',\n        paddingBottom: 12,\n      }}\n      bodyStyle={{\n        padding: '20px',\n      }}\n    >\n      {statsError ? (\n        <Alert\n          message=\"数据概览加载失败\"\n          description={statsError}\n          type=\"error\"\n          showIcon\n          style={{\n            borderRadius: 8,\n          }}\n        />\n      ) : (\n        <Spin spinning={statsLoading}>\n          {/* 使用 StatisticCard.Group 组件的响应式布局 */}\n          <StatisticCard.Group direction={screens?.md ? 'row' : 'column'}>\n            {/* 车辆统计 */}\n            <StatisticCard\n          \n            >\n              <Flex align=\"center\" justify=\"flex-start\" style={{ height: '100%', padding: '16px 12px' }}>\n                {/* 左侧图标 */}\n                <CarOutlined style={{ color: '#1890ff', fontSize: 24, marginRight: 16 }} />\n                {/* 右侧标题和数值 */}\n                <Flex vertical align=\"flex-start\" justify=\"center\" style={{ flex: 1 }}>\n                  <Typography.Text style={{ fontSize: 14, color: '#666', marginBottom: 4 }}>\n                    车辆\n                  </Typography.Text>\n                  <Typography.Text style={{\n                    color: '#1890ff',\n                    fontSize: 32,\n                    fontWeight: 700,\n                    lineHeight: 1,\n                  }}>\n                    {personalStats.vehicles}\n                  </Typography.Text>\n                </Flex>\n              </Flex>\n            </StatisticCard>\n\n            {/* 人员统计 */}\n            <StatisticCard\n         \n            >\n              <Flex align=\"center\" justify=\"flex-start\" style={{ height: '100%', padding: '16px 12px' }}>\n                {/* 左侧图标 */}\n                <UsergroupAddOutlined style={{ color: '#52c41a', fontSize: 24, marginRight: 16 }} />\n                {/* 右侧标题和数值 */}\n                <Flex vertical align=\"flex-start\" justify=\"center\" style={{ flex: 1 }}>\n                  <Typography.Text style={{ fontSize: 14, color: '#666', marginBottom: 4 }}>\n                    人员\n                  </Typography.Text>\n                  <Typography.Text style={{\n                    color: '#52c41a',\n                    fontSize: 32,\n                    fontWeight: 700,\n                    lineHeight: 1,\n                  }}>\n                    {personalStats.personnel}\n                  </Typography.Text>\n                </Flex>\n              </Flex>\n            </StatisticCard>\n\n            {/* 预警统计 */}\n            <StatisticCard\n             \n            >\n              <Flex align=\"center\" justify=\"flex-start\" style={{ height: '100%', padding: '16px 12px' }}>\n                {/* 左侧图标 */}\n                <ExclamationCircleOutlined style={{ color: '#faad14', fontSize: 24, marginRight: 16 }} />\n                {/* 右侧标题和数值 */}\n                <Flex vertical align=\"flex-start\" justify=\"center\" style={{ flex: 1 }}>\n                  <Typography.Text style={{ fontSize: 14, color: '#666', marginBottom: 4 }}>\n                    预警\n                  </Typography.Text>\n                  <Typography.Text style={{\n                    color: '#faad14',\n                    fontSize: 32,\n                    fontWeight: 700,\n                    lineHeight: 1,\n                  }}>\n                    {personalStats.warnings}\n                  </Typography.Text>\n                </Flex>\n              </Flex>\n            </StatisticCard>\n\n            {/* 告警统计 */}\n            <StatisticCard\n          \n            >\n              <Flex align=\"center\" justify=\"flex-start\" style={{ height: '100%', padding: '16px 12px' }}>\n                {/* 左侧图标 */}\n                <AlertOutlined style={{ color: '#ff4d4f', fontSize: 24, marginRight: 16 }} />\n                {/* 右侧标题和数值 */}\n                <Flex vertical align=\"flex-start\" justify=\"center\" style={{ flex: 1 }}>\n                  <Typography.Text style={{ fontSize: 14, color: '#666', marginBottom: 4 }}>\n                    告警\n                  </Typography.Text>\n                  <Typography.Text style={{\n                    color: '#ff4d4f',\n                    fontSize: 32,\n                    fontWeight: 700,\n                    lineHeight: 1,\n                  }}>\n                    {personalStats.alerts}\n                  </Typography.Text>\n                </Flex>\n              </Flex>\n            </StatisticCard>\n          </StatisticCard.Group>\n        </Spin>\n      )}\n    </ProCard>\n  );\n};\n\nexport default DataOverview;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCoNb;;;2BAAA;;;;;;0CAjNO;yCAOA;kDACgC;oFACI;yCACf;;;;;;;;;;YAG5B;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,MAAM,eAAyB;;gBAC7B;;GAEC,GACD,MAAM,EAAE,aAAa,EAAE,GAAG,UAAI;gBAC9B,MAAM,UAAU;gBAEhB;;GAEC,GACD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAA4B;oBAC5E,UAAU;oBACV,WAAW;oBACX,UAAU;oBACV,QAAQ;gBACV;gBAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;gBACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAgB;gBAE5D,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,MAAM,iBAAiB;wBACrB,IAAI;4BACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,oBAAoB;4BACpD,iBAAiB;4BACjB,cAAc;wBAChB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,aAAa;4BAC3B,cAAc;wBAChB,SAAU;4BACR,gBAAgB;wBAClB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,qBACE,2BAAC,sBAAO;oBACN,qBACE,2BAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,YAAY;4BAAU,KAAK;wBAAE;;0CAC1D,2BAAC,uBAAgB;gCAAC,OAAO;oCAAE,UAAU;oCAAI,OAAO;gCAAU;;;;;;0CAC1D,2BAAC;0CAAK;;;;;;;;;;;;oBAGV,OAAO;wBACL,cAAc;wBACd,cAAc;wBACd,QAAQ;oBACV;oBACA,WAAW;wBACT,cAAc;wBACd,eAAe;oBACjB;oBACA,WAAW;wBACT,SAAS;oBACX;8BAEC,2BACC,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAa;wBACb,MAAK;wBACL,QAAQ;wBACR,OAAO;4BACL,cAAc;wBAChB;;;;;6CAGF,2BAAC,UAAI;wBAAC,UAAU;kCAEd,cAAA,2BAAC,4BAAa,CAAC,KAAK;4BAAC,WAAW,CAAA,oBAAA,8BAAA,QAAS,EAAE,IAAG,QAAQ;;8CAEpD,2BAAC,4BAAa;8CAGZ,cAAA,2BAAC,UAAI;wCAAC,OAAM;wCAAS,SAAQ;wCAAa,OAAO;4CAAE,QAAQ;4CAAQ,SAAS;wCAAY;;0DAEtF,2BAAC,kBAAW;gDAAC,OAAO;oDAAE,OAAO;oDAAW,UAAU;oDAAI,aAAa;gDAAG;;;;;;0DAEtE,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,OAAM;gDAAa,SAAQ;gDAAS,OAAO;oDAAE,MAAM;gDAAE;;kEAClE,2BAAC,gBAAU,CAAC,IAAI;wDAAC,OAAO;4DAAE,UAAU;4DAAI,OAAO;4DAAQ,cAAc;wDAAE;kEAAG;;;;;;kEAG1E,2BAAC,gBAAU,CAAC,IAAI;wDAAC,OAAO;4DACtB,OAAO;4DACP,UAAU;4DACV,YAAY;4DACZ,YAAY;wDACd;kEACG,cAAc,QAAQ;;;;;;;;;;;;;;;;;;;;;;;8CAO/B,2BAAC,4BAAa;8CAGZ,cAAA,2BAAC,UAAI;wCAAC,OAAM;wCAAS,SAAQ;wCAAa,OAAO;4CAAE,QAAQ;4CAAQ,SAAS;wCAAY;;0DAEtF,2BAAC,2BAAoB;gDAAC,OAAO;oDAAE,OAAO;oDAAW,UAAU;oDAAI,aAAa;gDAAG;;;;;;0DAE/E,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,OAAM;gDAAa,SAAQ;gDAAS,OAAO;oDAAE,MAAM;gDAAE;;kEAClE,2BAAC,gBAAU,CAAC,IAAI;wDAAC,OAAO;4DAAE,UAAU;4DAAI,OAAO;4DAAQ,cAAc;wDAAE;kEAAG;;;;;;kEAG1E,2BAAC,gBAAU,CAAC,IAAI;wDAAC,OAAO;4DACtB,OAAO;4DACP,UAAU;4DACV,YAAY;4DACZ,YAAY;wDACd;kEACG,cAAc,SAAS;;;;;;;;;;;;;;;;;;;;;;;8CAOhC,2BAAC,4BAAa;8CAGZ,cAAA,2BAAC,UAAI;wCAAC,OAAM;wCAAS,SAAQ;wCAAa,OAAO;4CAAE,QAAQ;4CAAQ,SAAS;wCAAY;;0DAEtF,2BAAC,gCAAyB;gDAAC,OAAO;oDAAE,OAAO;oDAAW,UAAU;oDAAI,aAAa;gDAAG;;;;;;0DAEpF,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,OAAM;gDAAa,SAAQ;gDAAS,OAAO;oDAAE,MAAM;gDAAE;;kEAClE,2BAAC,gBAAU,CAAC,IAAI;wDAAC,OAAO;4DAAE,UAAU;4DAAI,OAAO;4DAAQ,cAAc;wDAAE;kEAAG;;;;;;kEAG1E,2BAAC,gBAAU,CAAC,IAAI;wDAAC,OAAO;4DACtB,OAAO;4DACP,UAAU;4DACV,YAAY;4DACZ,YAAY;wDACd;kEACG,cAAc,QAAQ;;;;;;;;;;;;;;;;;;;;;;;8CAO/B,2BAAC,4BAAa;8CAGZ,cAAA,2BAAC,UAAI;wCAAC,OAAM;wCAAS,SAAQ;wCAAa,OAAO;4CAAE,QAAQ;4CAAQ,SAAS;wCAAY;;0DAEtF,2BAAC,oBAAa;gDAAC,OAAO;oDAAE,OAAO;oDAAW,UAAU;oDAAI,aAAa;gDAAG;;;;;;0DAExE,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,OAAM;gDAAa,SAAQ;gDAAS,OAAO;oDAAE,MAAM;gDAAE;;kEAClE,2BAAC,gBAAU,CAAC,IAAI;wDAAC,OAAO;4DAAE,UAAU;4DAAI,OAAO;4DAAQ,cAAc;wDAAE;kEAAG;;;;;;kEAG1E,2BAAC,gBAAU,CAAC,IAAI;wDAAC,OAAO;4DACtB,OAAO;4DACP,UAAU;4DACV,YAAY;4DACZ,YAAY;wDACd;kEACG,cAAc,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUzC;eA7KM;iBAAA;gBA+KN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDpND;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}